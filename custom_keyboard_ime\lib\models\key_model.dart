class KeyModel {
  final String displayText;
  final String value;
  final KeyType type;

  KeyModel(
    this.displayText,
    this.value, {
    this.type = KeyType.character,
  });

  // Factory constructors for special keys
  factory KeyModel.space() => KeyModel('Space', ' ', type: KeyType.space);
  factory KeyModel.backspace() => KeyModel('⌫', 'backspace', type: KeyType.backspace);
  factory KeyModel.enter() => KeyModel('↵', 'enter', type: KeyType.enter);
  factory KeyModel.shift() => KeyModel('⇧', 'shift', type: KeyType.shift);
  factory KeyModel.numbers() => KeyModel('123', 'numbers', type: KeyType.numbers);

  bool get isCharacter => type == KeyType.character;
  bool get isSpecial => type != KeyType.character;
}

enum KeyType {
  character,
  space,
  backspace,
  enter,
  shift,
  numbers,
}
