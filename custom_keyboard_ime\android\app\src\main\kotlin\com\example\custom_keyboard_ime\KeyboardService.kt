package com.example.custom_keyboard_ime

import android.inputmethodservice.InputMethodService
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputConnection
import io.flutter.embedding.android.FlutterView
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel

class KeyboardService : InputMethodService() {
    private var flutterEngine: FlutterEngine? = null
    private var flutterView: FlutterView? = null
    private var methodChannel: MethodChannel? = null
    
    companion object {
        private const val CHANNEL = "custom_keyboard_ime/keyboard"
    }

    override fun onCreate() {
        super.onCreate()
        setupFlutter()
    }

    private fun setupFlutter() {
        // Initialize Flutter engine
        flutterEngine = FlutterEngine(this)
        
        // Start executing Dart code
        flutterEngine?.dartExecutor?.executeDartEntrypoint(
            DartExecutor.DartEntrypoint.createDefault()
        )
        
        // Create Flutter view
        flutterView = FlutterView(this)
        flutterView?.attachToFlutterEngine(flutterEngine!!)
        
        // Setup method channel for communication between Flutter and Android
        methodChannel = MethodChannel(
            flutterEngine?.dartExecutor?.binaryMessenger!!,
            CHANNEL
        )
        
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "sendText" -> {
                    val text = call.argument<String>("text")
                    if (text != null) {
                        sendText(text)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Text cannot be null", null)
                    }
                }
                "deleteText" -> {
                    deleteText()
                    result.success(null)
                }
                "sendKeyEvent" -> {
                    val keyCode = call.argument<Int>("keyCode")
                    if (keyCode != null) {
                        sendKeyEvent(keyCode)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "KeyCode cannot be null", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onCreateInputView(): View? {
        return flutterView
    }

    override fun onStartInputView(info: EditorInfo?, restarting: Boolean) {
        super.onStartInputView(info, restarting)
        
        // Notify Flutter about input start
        methodChannel?.invokeMethod("onInputStart", mapOf(
            "inputType" to info?.inputType,
            "packageName" to info?.packageName,
            "fieldName" to info?.fieldName
        ))
    }

    override fun onFinishInputView(finishingInput: Boolean) {
        super.onFinishInputView(finishingInput)
        
        // Notify Flutter about input finish
        methodChannel?.invokeMethod("onInputFinish", null)
    }

    private fun sendText(text: String) {
        val ic: InputConnection? = currentInputConnection
        ic?.commitText(text, 1)
    }

    private fun deleteText() {
        val ic: InputConnection? = currentInputConnection
        ic?.deleteSurroundingText(1, 0)
    }

    private fun sendKeyEvent(keyCode: Int) {
        val ic: InputConnection? = currentInputConnection
        ic?.sendKeyEvent(android.view.KeyEvent(android.view.KeyEvent.ACTION_DOWN, keyCode))
        ic?.sendKeyEvent(android.view.KeyEvent(android.view.KeyEvent.ACTION_UP, keyCode))
    }

    override fun onDestroy() {
        super.onDestroy()
        flutterView?.detachFromFlutterEngine()
        flutterEngine?.destroy()
    }
}
