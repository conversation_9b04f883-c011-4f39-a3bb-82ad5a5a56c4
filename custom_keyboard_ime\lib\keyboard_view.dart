import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'models/key_model.dart';
import 'widgets/keyboard_key.dart';
import 'services/keyboard_service.dart';

class KeyboardView extends StatefulWidget {
  const KeyboardView({super.key});

  @override
  State<KeyboardView> createState() => _KeyboardViewState();
}

class _KeyboardViewState extends State<KeyboardView> {
  final KeyboardService _keyboardService = KeyboardService();
  bool _isShiftPressed = false;
  bool _isCapsLock = false;

  @override
  void initState() {
    super.initState();
    _keyboardService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[200],
      body: Container(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // First row: Q W E R T Y U I O P
            _buildKeyboardRow([
              KeyModel('Q', 'q'),
              KeyModel('W', 'w'),
              KeyModel('E', 'e'),
              KeyModel('R', 'r'),
              KeyModel('T', 't'),
              KeyModel('Y', 'y'),
              KeyModel('U', 'u'),
              KeyModel('I', 'i'),
              KeyModel('O', 'o'),
              KeyModel('P', 'p'),
            ]),
            const SizedBox(height: 8),
            
            // Second row: A S D F G H J K L
            _buildKeyboardRow([
              KeyModel('A', 'a'),
              KeyModel('S', 's'),
              KeyModel('D', 'd'),
              KeyModel('F', 'f'),
              KeyModel('G', 'g'),
              KeyModel('H', 'h'),
              KeyModel('J', 'j'),
              KeyModel('K', 'k'),
              KeyModel('L', 'l'),
            ]),
            const SizedBox(height: 8),
            
            // Third row: Shift Z X C V B N M Backspace
            Row(
              children: [
                // Shift key
                Expanded(
                  flex: 2,
                  child: KeyboardKey(
                    keyModel: KeyModel('⇧', 'shift'),
                    onPressed: _handleShiftPress,
                    isPressed: _isShiftPressed || _isCapsLock,
                    backgroundColor: _isShiftPressed || _isCapsLock 
                        ? Colors.blue[300] 
                        : Colors.white,
                  ),
                ),
                const SizedBox(width: 4),
                
                // Letter keys
                ..._buildKeyList([
                  KeyModel('Z', 'z'),
                  KeyModel('X', 'x'),
                  KeyModel('C', 'c'),
                  KeyModel('V', 'v'),
                  KeyModel('B', 'b'),
                  KeyModel('N', 'n'),
                  KeyModel('M', 'm'),
                ]),
                const SizedBox(width: 4),
                
                // Backspace key
                Expanded(
                  flex: 2,
                  child: KeyboardKey(
                    keyModel: KeyModel('⌫', 'backspace'),
                    onPressed: () => _keyboardService.deleteText(),
                    backgroundColor: Colors.grey[300],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Fourth row: Numbers and Space
            Row(
              children: [
                // Number toggle (123)
                Expanded(
                  flex: 2,
                  child: KeyboardKey(
                    keyModel: KeyModel('123', 'numbers'),
                    onPressed: () {
                      // TODO: Implement number keyboard toggle
                    },
                    backgroundColor: Colors.grey[300],
                  ),
                ),
                const SizedBox(width: 4),
                
                // Space bar
                Expanded(
                  flex: 6,
                  child: KeyboardKey(
                    keyModel: KeyModel('Space', ' '),
                    onPressed: () => _keyboardService.sendText(' '),
                    backgroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 4),
                
                // Enter key
                Expanded(
                  flex: 2,
                  child: KeyboardKey(
                    keyModel: KeyModel('↵', 'enter'),
                    onPressed: () => _keyboardService.sendKeyEvent(66), // KeyEvent.KEYCODE_ENTER
                    backgroundColor: Colors.blue[300],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyboardRow(List<KeyModel> keys) {
    return Row(
      children: _buildKeyList(keys),
    );
  }

  List<Widget> _buildKeyList(List<KeyModel> keys) {
    return keys.map((key) {
      return Expanded(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2.0),
          child: KeyboardKey(
            keyModel: key,
            onPressed: () => _handleKeyPress(key),
            backgroundColor: Colors.white,
          ),
        ),
      );
    }).toList();
  }

  void _handleKeyPress(KeyModel key) {
    String textToSend = key.value;
    
    // Handle case conversion based on shift/caps lock state
    if (key.displayText.length == 1 && key.displayText.contains(RegExp(r'[A-Za-z]'))) {
      if (_isShiftPressed || _isCapsLock) {
        textToSend = key.displayText.toUpperCase();
      } else {
        textToSend = key.value.toLowerCase();
      }
    }
    
    _keyboardService.sendText(textToSend);
    
    // Reset shift state after key press (but not caps lock)
    if (_isShiftPressed && !_isCapsLock) {
      setState(() {
        _isShiftPressed = false;
      });
    }
  }

  void _handleShiftPress() {
    setState(() {
      if (_isShiftPressed) {
        // Double tap for caps lock
        _isCapsLock = !_isCapsLock;
        _isShiftPressed = false;
      } else {
        _isShiftPressed = true;
        _isCapsLock = false;
      }
    });
  }

  @override
  void dispose() {
    _keyboardService.dispose();
    super.dispose();
  }
}
