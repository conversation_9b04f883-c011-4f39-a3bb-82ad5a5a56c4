import 'package:flutter/services.dart';

class KeyboardService {
  static const MethodChannel _channel = MethodChannel('custom_keyboard_ime/keyboard');
  
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Set up method call handler for receiving messages from Android
      _channel.setMethodCallHandler(_handleMethodCall);
      _isInitialized = true;
      print('KeyboardService initialized successfully');
    } catch (e) {
      print('Error initializing KeyboardService: $e');
    }
  }

  Future<void> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onInputStart':
        _handleInputStart(call.arguments);
        break;
      case 'onInputFinish':
        _handleInputFinish();
        break;
      default:
        print('Unknown method call: ${call.method}');
    }
  }

  void _handleInputStart(dynamic arguments) {
    print('Input started: $arguments');
    // Handle when input field becomes active
    // You can use this to adjust keyboard behavior based on input type
  }

  void _handleInputFinish() {
    print('Input finished');
    // Handle when input field loses focus
  }

  Future<void> sendText(String text) async {
    if (!_isInitialized) {
      print('KeyboardService not initialized');
      return;
    }

    try {
      await _channel.invokeMethod('sendText', {'text': text});
    } catch (e) {
      print('Error sending text: $e');
    }
  }

  Future<void> deleteText() async {
    if (!_isInitialized) {
      print('KeyboardService not initialized');
      return;
    }

    try {
      await _channel.invokeMethod('deleteText');
    } catch (e) {
      print('Error deleting text: $e');
    }
  }

  Future<void> sendKeyEvent(int keyCode) async {
    if (!_isInitialized) {
      print('KeyboardService not initialized');
      return;
    }

    try {
      await _channel.invokeMethod('sendKeyEvent', {'keyCode': keyCode});
    } catch (e) {
      print('Error sending key event: $e');
    }
  }

  void dispose() {
    _isInitialized = false;
  }
}
