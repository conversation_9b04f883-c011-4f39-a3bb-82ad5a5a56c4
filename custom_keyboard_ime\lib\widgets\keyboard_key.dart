import 'package:flutter/material.dart';
import '../models/key_model.dart';

class Custom<PERSON>eyboardKey extends StatefulWidget {
  final KeyModel keyModel;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final bool isPressed;

  const CustomKeyboardKey({
    super.key,
    required this.keyModel,
    required this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.isPressed = false,
  });

  @override
  State<CustomKeyboardKey> createState() => _CustomKeyboardKeyState();
}

class _CustomKeyboardKeyState extends State<CustomKeyboardKey>
    with SingleTickerProviderStateMixin {
  bool _isPressed = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() {
          _isPressed = true;
        });
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
        widget.onPressed();
      },
      onTapCancel: () {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              height: 48,
              margin: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: _getBackgroundColor(),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
                boxShadow: _isPressed
                    ? []
                    : [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                        ),
                      ],
              ),
              child: Center(
                child: Text(
                  widget.keyModel.displayText,
                  style: TextStyle(
                    fontSize: _getFontSize(),
                    fontWeight: FontWeight.w500,
                    color: widget.textColor ?? Colors.black87,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Color _getBackgroundColor() {
    if (widget.isPressed) {
      return widget.backgroundColor ?? Colors.blue[300]!;
    }
    if (_isPressed) {
      return (widget.backgroundColor ?? Colors.white).withOpacity(0.8);
    }
    return widget.backgroundColor ?? Colors.white;
  }

  double _getFontSize() {
    switch (widget.keyModel.type) {
      case KeyType.character:
        return 18;
      case KeyType.space:
        return 14;
      case KeyType.backspace:
      case KeyType.enter:
      case KeyType.shift:
        return 16;
      case KeyType.numbers:
        return 14;
    }
  }
}
